{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Poker Trainer - GTO Decision Making\n", "\n", "This notebook simulates a poker table and tests your decision-making skills using Game Theory Optimal (GTO) principles."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import random\n", "import itertools\n", "from collections import Counter\n", "from IPython.display import display, HTML, clear_output\n", "import ipywidgets as widgets\n", "from ipywidgets import interact, interactive, fixed, interact_manual\n", "\n", "# Card representation\n", "SUITS = ['♠', '♥', '♦', '♣']\n", "RANKS = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']\n", "RANK_VALUES = {rank: i for i, rank in enumerate(RANKS)}\n", "\n", "class Card:\n", "    def __init__(self, rank, suit):\n", "        self.rank = rank\n", "        self.suit = suit\n", "        self.value = RANK_VALUES[rank]\n", "    \n", "    def __str__(self):\n", "        return f\"{self.rank}{self.suit}\"\n", "    \n", "    def __repr__(self):\n", "        return self.__str__()\n", "    \n", "    def display_html(self):\n", "        color = 'red' if self.suit in ['♥', '♦'] else 'black'\n", "        return f'<span style=\"color: {color}; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">{self.rank}{self.suit}</span>'\n", "\n", "class Deck:\n", "    def __init__(self):\n", "        self.cards = [Card(rank, suit) for suit in SUITS for rank in RANKS]\n", "        self.shuffle()\n", "    \n", "    def shuffle(self):\n", "        random.shuffle(self.cards)\n", "    \n", "    def deal(self, num_cards=1):\n", "        return [self.cards.pop() for _ in range(num_cards)]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Hand evaluation functions\n", "def evaluate_hand(cards):\n", "    \"\"\"Evaluate a 5-7 card poker hand and return the best 5-card hand\"\"\"\n", "    if len(cards) < 5:\n", "        return None, 0\n", "    \n", "    best_hand = None\n", "    best_rank = 0\n", "    \n", "    # Try all combinations of 5 cards\n", "    for combo in itertools.combinations(cards, 5):\n", "        hand_rank = get_hand_rank(list(combo))\n", "        if hand_rank > best_rank:\n", "            best_rank = hand_rank\n", "            best_hand = list(combo)\n", "    \n", "    return best_hand, best_rank\n", "\n", "def get_hand_rank(cards):\n", "    \"\"\"Get numerical rank of a 5-card hand (higher = better)\"\"\"\n", "    ranks = sorted([card.value for card in cards], reverse=True)\n", "    suits = [card.suit for card in cards]\n", "    \n", "    is_flush = len(set(suits)) == 1\n", "    is_straight = ranks == list(range(ranks[0], ranks[0] - 5, -1))\n", "    \n", "    # Special case for A-2-3-4-5 straight\n", "    if ranks == [12, 3, 2, 1, 0]:  # A, 5, 4, 3, 2\n", "        is_straight = True\n", "        ranks = [3, 2, 1, 0, 12]  # Treat ace as low\n", "    \n", "    rank_counts = Counter(ranks)\n", "    counts = sorted(rank_counts.values(), reverse=True)\n", "    \n", "    # Hand rankings (higher number = better hand)\n", "    if is_straight and is_flush:\n", "        return 8000000 + ranks[0]  # Straight flush\n", "    elif counts == [4, 1]:\n", "        return 7000000 + max(rank_counts, key=rank_counts.get) * 1000 + min(rank_counts, key=rank_counts.get)  # Four of a kind\n", "    elif counts == [3, 2]:\n", "        trips = max(rank_counts, key=rank_counts.get)\n", "        pair = min(rank_counts, key=rank_counts.get)\n", "        return 6000000 + trips * 1000 + pair  # Full house\n", "    elif is_flush:\n", "        return 5000000 + sum(rank * (13 ** i) for i, rank in enumerate(ranks))  # Flush\n", "    elif is_straight:\n", "        return 4000000 + ranks[0]  # Straight\n", "    elif counts == [3, 1, 1]:\n", "        trips = max(rank_counts, key=rank_counts.get)\n", "        kickers = sorted([r for r in ranks if r != trips], reverse=True)\n", "        return 3000000 + trips * 10000 + kickers[0] * 100 + kickers[1]  # Three of a kind\n", "    elif counts == [2, 2, 1]:\n", "        pairs = sorted([r for r, c in rank_counts.items() if c == 2], reverse=True)\n", "        kicker = [r for r, c in rank_counts.items() if c == 1][0]\n", "        return 2000000 + pairs[0] * 10000 + pairs[1] * 100 + kicker  # Two pair\n", "    elif counts == [2, 1, 1, 1]:\n", "        pair = [r for r, c in rank_counts.items() if c == 2][0]\n", "        kickers = sorted([r for r in ranks if r != pair], reverse=True)\n", "        return 1000000 + pair * 100000 + sum(k * (13 ** i) for i, k in enumerate(kickers))  # One pair\n", "    else:\n", "        return sum(rank * (13 ** i) for i, rank in enumerate(ranks))  # High card\n", "\n", "def get_hand_name(cards):\n", "    \"\"\"Get the name of a poker hand\"\"\"\n", "    if not cards:\n", "        return \"No hand\"\n", "    \n", "    ranks = sorted([card.value for card in cards], reverse=True)\n", "    suits = [card.suit for card in cards]\n", "    \n", "    is_flush = len(set(suits)) == 1\n", "    is_straight = ranks == list(range(ranks[0], ranks[0] - 5, -1))\n", "    \n", "    # Special case for A-2-3-4-5 straight\n", "    if ranks == [12, 3, 2, 1, 0]:\n", "        is_straight = True\n", "    \n", "    rank_counts = Counter(ranks)\n", "    counts = sorted(rank_counts.values(), reverse=True)\n", "    \n", "    if is_straight and is_flush:\n", "        return \"Straight Flush\"\n", "    elif counts == [4, 1]:\n", "        return \"Four of a Kind\"\n", "    elif counts == [3, 2]:\n", "        return \"Full House\"\n", "    elif is_flush:\n", "        return \"Flush\"\n", "    elif is_straight:\n", "        return \"Straight\"\n", "    elif counts == [3, 1, 1]:\n", "        return \"Three of a Kind\"\n", "    elif counts == [2, 2, 1]:\n", "        return \"Two Pair\"\n", "    elif counts == [2, 1, 1, 1]:\n", "        return \"One Pair\"\n", "    else:\n", "        return \"High Card\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Poker odds and equity calculations\n", "def calculate_equity(hole_cards, community_cards, num_simulations=10000):\n", "    \"\"\"Calculate equity using Monte Carlo simulation\"\"\"\n", "    if len(community_cards) == 5:\n", "        # All cards dealt, just evaluate\n", "        best_hand, hand_rank = evaluate_hand(hole_cards + community_cards)\n", "        return 1.0 if hand_rank > 0 else 0.0\n", "    \n", "    wins = 0\n", "    used_cards = set(str(card) for card in hole_cards + community_cards)\n", "    \n", "    for _ in range(num_simulations):\n", "        # Create a deck without used cards\n", "        available_cards = [Card(rank, suit) for suit in SUITS for rank in RANKS \n", "                          if f\"{rank}{suit}\" not in used_cards]\n", "        random.shuffle(available_cards)\n", "        \n", "        # Complete the community cards\n", "        cards_needed = 5 - len(community_cards)\n", "        simulated_community = community_cards + available_cards[:cards_needed]\n", "        \n", "        # Simulate opponent with random hole cards\n", "        opponent_cards = available_cards[cards_needed:cards_needed + 2]\n", "        \n", "        # Evaluate both hands\n", "        player_hand, player_rank = evaluate_hand(hole_cards + simulated_community)\n", "        opponent_hand, opponent_rank = evaluate_hand(opponent_cards + simulated_community)\n", "        \n", "        if player_rank > opponent_rank:\n", "            wins += 1\n", "        elif player_rank == opponent_rank:\n", "            wins += 0.5  # Tie\n", "    \n", "    return wins / num_simulations\n", "\n", "def get_outs(hole_cards, community_cards):\n", "    \"\"\"Calculate outs for improving hand\"\"\"\n", "    if len(community_cards) >= 5:\n", "        return 0\n", "    \n", "    current_best, current_rank = evaluate_hand(hole_cards + community_cards)\n", "    used_cards = set(str(card) for card in hole_cards + community_cards)\n", "    \n", "    outs = 0\n", "    for rank in RANKS:\n", "        for suit in SUITS:\n", "            if f\"{rank}{suit}\" not in used_cards:\n", "                test_card = Card(rank, suit)\n", "                test_community = community_cards + [test_card]\n", "                if len(test_community) <= 5:\n", "                    test_hand, test_rank = evaluate_hand(hole_cards + test_community)\n", "                    if test_rank > current_rank:\n", "                        outs += 1\n", "    \n", "    return outs\n", "\n", "def pot_odds_decision(pot_size, bet_size, equity):\n", "    \"\"\"Calculate if a call is profitable based on pot odds\"\"\"\n", "    pot_odds = bet_size / (pot_size + bet_size)\n", "    return equity > pot_odds, pot_odds\n", "\n", "def gto_action_recommendation(equity, position, pot_size, bet_size, stack_size):\n", "    \"\"\"Simplified GTO recommendation based on equity and position\"\"\"\n", "    pot_odds = bet_size / (pot_size + bet_size) if bet_size > 0 else 0\n", "    \n", "    # Simplified GTO thresholds\n", "    if equity > 0.65:\n", "        return \"RAISE\" if stack_size > bet_size * 3 else \"CALL\"\n", "    elif equity > pot_odds + 0.05:  # Small edge for position\n", "        return \"CALL\"\n", "    elif equity > 0.3 and bet_size == 0:  # Check with decent equity\n", "        return \"CHECK\"\n", "    elif bet_size == 0 and equity < 0.3:\n", "        return \"CHECK\"  # Don't bet weak hands\n", "    else:\n", "        return \"FOLD\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Poker Trainer initialized! Run the next cell to start playing.\n"]}], "source": ["# Poker game simulation\n", "class PokerGame:\n", "    def __init__(self):\n", "        self.deck = Deck()\n", "        self.hole_cards = []\n", "        self.community_cards = []\n", "        self.pot_size = 100\n", "        self.current_bet = 0\n", "        self.player_stack = 1000\n", "        self.stage = \"preflop\"  # preflop, flop, turn, river\n", "        self.score = 0\n", "        self.questions_asked = 0\n", "    \n", "    def start_new_hand(self):\n", "        self.deck = Deck()\n", "        self.hole_cards = self.deck.deal(2)\n", "        self.community_cards = []\n", "        self.pot_size = 100\n", "        self.current_bet = 0\n", "        self.stage = \"preflop\"\n", "    \n", "    def deal_flop(self):\n", "        if self.stage == \"preflop\":\n", "            self.community_cards.extend(self.deck.deal(3))\n", "            self.stage = \"flop\"\n", "    \n", "    def deal_turn(self):\n", "        if self.stage == \"flop\":\n", "            self.community_cards.extend(self.deck.deal(1))\n", "            self.stage = \"turn\"\n", "    \n", "    def deal_river(self):\n", "        if self.stage == \"turn\":\n", "            self.community_cards.extend(self.deck.deal(1))\n", "            self.stage = \"river\"\n", "    \n", "    def display_table(self):\n", "        \"\"\"Display the current poker table state\"\"\"\n", "        print(\"\\n\" + \"=\"*60)\n", "        print(f\"POKER TRAINER - {self.stage.upper()} STAGE\")\n", "        print(\"=\"*60)\n", "        \n", "        # Display hole cards\n", "        hole_html = \"<h3>Your Hole Cards:</h3>\" + \"\".join([card.display_html() for card in self.hole_cards])\n", "        display(HTML(hole_html))\n", "        \n", "        # Display community cards\n", "        if self.community_cards:\n", "            community_html = \"<h3>Community Cards:</h3>\" + \"\".join([card.display_html() for card in self.community_cards])\n", "            display(HTML(community_html))\n", "        \n", "        # Display current hand strength\n", "        if len(self.community_cards) >= 3:\n", "            best_hand, _ = evaluate_hand(self.hole_cards + self.community_cards)\n", "            hand_name = get_hand_name(best_hand)\n", "            print(f\"\\nCurrent Best Hand: {hand_name}\")\n", "        \n", "        print(f\"\\nPot Size: ${self.pot_size}\")\n", "        print(f\"Current Bet: ${self.current_bet}\")\n", "        print(f\"Your Stack: ${self.player_stack}\")\n", "        print(f\"Score: {self.score}/{self.questions_asked}\")\n", "    \n", "    def get_equity(self):\n", "        return calculate_equity(self.hole_cards, self.community_cards, 5000)\n", "    \n", "    def get_gto_action(self):\n", "        equity = self.get_equity()\n", "        return gto_action_recommendation(equity, \"middle\", self.pot_size, self.current_bet, self.player_stack)\n", "\n", "# Initialize game\n", "game = PokerGame()\n", "print(\"Poker Trainer initialized! Run the next cell to start playing.\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Interactive quiz functions\n", "def ask_hand_ranking_question():\n", "    \"\"\"Ask player to identify the most common hands\"\"\"\n", "    game.display_table()\n", "    \n", "    if len(game.community_cards) < 3:\n", "        print(\"\\nNot enough community cards for hand ranking question.\")\n", "        return\n", "    \n", "    # Calculate possible hands\n", "    all_cards = game.hole_cards + game.community_cards\n", "    current_hand, _ = evaluate_hand(all_cards)\n", "    current_hand_name = get_hand_name(current_hand)\n", "    \n", "    # Generate options\n", "    hand_types = [\"High Card\", \"One Pair\", \"Two Pair\", \"Three of a Kind\", \n", "                  \"Straight\", \"Flush\", \"Full House\", \"Four of a Kind\", \"Straight Flush\"]\n", "    \n", "    # Create multiple choice\n", "    options = [current_hand_name]\n", "    while len(options) < 4:\n", "        option = random.choice(hand_types)\n", "        if option not in options:\n", "            options.append(option)\n", "    \n", "    random.shuffle(options)\n", "    correct_index = options.index(current_hand_name)\n", "    \n", "    print(\"\\n🎯 HAND RANKING QUESTION:\")\n", "    print(\"What is your current best hand?\")\n", "    for i, option in enumerate(options):\n", "        print(f\"{i+1}. {option}\")\n", "    \n", "    try:\n", "        answer = int(input(\"\\nEnter your choice (1-4): \")) - 1\n", "        game.questions_asked += 1\n", "        \n", "        if answer == correct_index:\n", "            print(\"✅ Correct! Well done!\")\n", "            game.score += 1\n", "        else:\n", "            print(f\"❌ Incorrect. The correct answer was: {current_hand_name}\")\n", "            \n", "        # Show the actual best 5-card hand\n", "        if current_hand:\n", "            hand_html = \"<h4>Your best 5-card hand:</h4>\" + \"\".join([card.display_html() for card in current_hand])\n", "            display(HTML(hand_html))\n", "    except (ValueError, IndexError):\n", "        print(\"Invalid input. Please enter a number between 1 and 4.\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def ask_gto_decision_question():\n", "    \"\"\"Ask player about optimal GTO decision\"\"\"\n", "    game.display_table()\n", "    \n", "    # Set up a betting scenario\n", "    scenarios = [\n", "        {\"bet\": 0, \"pot\": 100, \"description\": \"No bet to you\"},\n", "        {\"bet\": 25, \"pot\": 100, \"description\": \"Opponent bets $25 into $100 pot\"},\n", "        {\"bet\": 50, \"pot\": 100, \"description\": \"Opponent bets $50 into $100 pot\"},\n", "        {\"bet\": 75, \"pot\": 150, \"description\": \"Opponent bets $75 into $150 pot\"}\n", "    ]\n", "    \n", "    scenario = random.choice(scenarios)\n", "    game.current_bet = scenario[\"bet\"]\n", "    game.pot_size = scenario[\"pot\"]\n", "    \n", "    print(f\"\\n🎯 GTO DECISION QUESTION:\")\n", "    print(f\"Scenario: {scenario['description']}\")\n", "    \n", "    # Calculate equity and GTO recommendation\n", "    equity = game.get_equity()\n", "    gto_action = game.get_gto_action()\n", "    \n", "    print(f\"\\nYour equity: {equity:.1%}\")\n", "    \n", "    if game.current_bet > 0:\n", "        pot_odds = game.current_bet / (game.pot_size + game.current_bet)\n", "        print(f\"Pot odds: {pot_odds:.1%}\")\n", "    \n", "    # Action options\n", "    if game.current_bet == 0:\n", "        options = [\"CHECK\", \"BET/RAISE\"]\n", "    else:\n", "        options = [\"FOLD\", \"CALL\", \"RAISE\"]\n", "    \n", "    print(\"\\nWhat should you do?\")\n", "    for i, option in enumerate(options):\n", "        print(f\"{i+1}. {option}\")\n", "    \n", "    try:\n", "        answer = int(input(f\"\\nEnter your choice (1-{len(options)}): \")) - 1\n", "        game.questions_asked += 1\n", "        \n", "        player_action = options[answer]\n", "        \n", "        # Check if answer matches GTO recommendation\n", "        correct = False\n", "        if gto_action == \"CHECK\" and player_action == \"CHECK\":\n", "            correct = True\n", "        elif gto_action == \"RAISE\" and player_action in [\"BET/RAISE\", \"RAISE\"]:\n", "            correct = True\n", "        elif gto_action == \"CALL\" and player_action == \"CALL\":\n", "            correct = True\n", "        elif gto_action == \"FOLD\" and player_action == \"FOLD\":\n", "            correct = True\n", "        \n", "        if correct:\n", "            print(\"✅ Correct! That's the GTO play!\")\n", "            game.score += 1\n", "        else:\n", "            print(f\"❌ Incorrect. GTO recommendation: {gto_action}\")\n", "        \n", "        # Explain the reasoning\n", "        print(f\"\\n📊 Analysis:\")\n", "        print(f\"Your equity: {equity:.1%}\")\n", "        if game.current_bet > 0:\n", "            pot_odds = game.current_bet / (game.pot_size + game.current_bet)\n", "            print(f\"Pot odds required: {pot_odds:.1%}\")\n", "            if equity > pot_odds:\n", "                print(\"✅ You have sufficient equity to call\")\n", "            else:\n", "                print(\"❌ Insufficient equity to call profitably\")\n", "        \n", "    except (ValueError, IndexError):\n", "        print(f\"Invalid input. Please enter a number between 1 and {len(options)}.\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def run_training_session():\n", "    \"\"\"Run a complete poker training session\"\"\"\n", "    print(\"🃏 Welcome to Poker Trainer!\")\n", "    print(\"This program will test your poker knowledge and GTO decision-making.\")\n", "    print(\"\\nInstructions:\")\n", "    print(\"- You'll be shown poker hands at different stages\")\n", "    print(\"- Answer questions about hand rankings and optimal decisions\")\n", "    print(\"- Learn from GTO analysis and improve your game!\")\n", "    \n", "    num_hands = 5  # Number of hands to practice\n", "    \n", "    for hand_num in range(1, num_hands + 1):\n", "        print(f\"\\n{'='*60}\")\n", "        print(f\"HAND {hand_num} of {num_hands}\")\n", "        print(f\"{'='*60}\")\n", "        \n", "        # Start new hand\n", "        game.start_new_hand()\n", "        \n", "        # Preflop - just show cards\n", "        print(\"\\n📋 PREFLOP:\")\n", "        game.display_table()\n", "        input(\"\\nPress Enter to see the flop...\")\n", "        \n", "        # Flop\n", "        game.deal_flop()\n", "        print(\"\\n📋 FLOP:\")\n", "        ask_hand_ranking_question()\n", "        input(\"\\nPress Enter to continue...\")\n", "        \n", "        # Ask GTO decision on flop\n", "        ask_gto_decision_question()\n", "        input(\"\\nPress Enter to see the turn...\")\n", "        \n", "        # Turn\n", "        game.deal_turn()\n", "        print(\"\\n📋 TURN:\")\n", "        ask_hand_ranking_question()\n", "        input(\"\\nPress Enter to continue...\")\n", "        \n", "        # Ask GTO decision on turn\n", "        ask_gto_decision_question()\n", "        input(\"\\nPress Enter to see the river...\")\n", "        \n", "        # River\n", "        game.deal_river()\n", "        print(\"\\n📋 RIVER:\")\n", "        ask_hand_ranking_question()\n", "        input(\"\\nPress Enter to continue...\")\n", "        \n", "        # Final GTO decision\n", "        ask_gto_decision_question()\n", "        \n", "        # Show final results for this hand\n", "        print(f\"\\n📊 Hand {hand_num} Complete!\")\n", "        print(f\"Current Score: {game.score}/{game.questions_asked} ({game.score/max(1,game.questions_asked)*100:.1f}%)\")\n", "        \n", "        if hand_num < num_hands:\n", "            input(\"\\nPress Enter for next hand...\")\n", "    \n", "    # Final results\n", "    print(f\"\\n{'='*60}\")\n", "    print(\"🏆 TRAINING SESSION COMPLETE!\")\n", "    print(f\"{'='*60}\")\n", "    print(f\"Final Score: {game.score}/{game.questions_asked}\")\n", "    print(f\"Accuracy: {game.score/max(1,game.questions_asked)*100:.1f}%\")\n", "    \n", "    if game.score/max(1,game.questions_asked) >= 0.8:\n", "        print(\"🌟 Excellent work! You have strong poker fundamentals!\")\n", "    elif game.score/max(1,game.questions_asked) >= 0.6:\n", "        print(\"👍 Good job! Keep practicing to improve your game.\")\n", "    else:\n", "        print(\"📚 Keep studying! Practice makes perfect in poker.\")\n", "    \n", "    print(\"\\nThanks for training! Run this cell again to practice more.\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🃏 Welcome to Poker Trainer!\n", "This program will test your poker knowledge and GTO decision-making.\n", "\n", "Instructions:\n", "- You'll be shown poker hands at different stages\n", "- Answer questions about hand rankings and optimal decisions\n", "- Learn from GTO analysis and improve your game!\n", "\n", "============================================================\n", "HAND 1 of 5\n", "============================================================\n", "\n", "📋 PREFLOP:\n", "\n", "============================================================\n", "POKER TRAINER - PREFLOP STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♠</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Pot Size: $100\n", "Current Bet: $0\n", "Your Stack: $1000\n", "Score: 0/0\n", "\n", "📋 FLOP:\n", "\n", "============================================================\n", "POKER TRAINER - FLOP STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♠</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">10♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: Three of a Kind\n", "\n", "Pot Size: $100\n", "Current Bet: $0\n", "Your Stack: $1000\n", "Score: 0/0\n", "\n", "🎯 HAND RANKING QUESTION:\n", "What is your current best hand?\n", "1. <PERSON><PERSON><PERSON>\n", "2. Three of a Kind\n", "3. Four of a Kind\n", "4. High Card\n", "✅ Correct! Well done!\n"]}, {"data": {"text/html": ["<h4>Your best 5-card hand:</h4><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♠</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♦</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">10♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "POKER TRAINER - FLOP STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♠</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">10♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: Three of a Kind\n", "\n", "Pot Size: $100\n", "Current Bet: $0\n", "Your Stack: $1000\n", "Score: 1/1\n", "\n", "🎯 GTO DECISION QUESTION:\n", "Sc<PERSON>rio: No bet to you\n", "\n", "Your equity: 86.3%\n", "\n", "What should you do?\n", "1. CHECK\n", "2. BET/RAISE\n", "✅ Correct! That's the GTO play!\n", "\n", "📊 Analysis:\n", "Your equity: 86.3%\n", "\n", "📋 TURN:\n", "\n", "============================================================\n", "POKER TRAINER - TURN STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♠</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">10♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">A♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: Three of a Kind\n", "\n", "Pot Size: $100\n", "Current Bet: $0\n", "Your Stack: $1000\n", "Score: 2/2\n", "\n", "🎯 HAND RANKING QUESTION:\n", "What is your current best hand?\n", "1. Three of a Kind\n", "2. Two Pair\n", "3. <PERSON>\n", "4. One Pair\n", "✅ Correct! Well done!\n"]}, {"data": {"text/html": ["<h4>Your best 5-card hand:</h4><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♠</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♦</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">10♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">A♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "POKER TRAINER - TURN STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♠</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">10♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">A♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: Three of a Kind\n", "\n", "Pot Size: $100\n", "Current Bet: $0\n", "Your Stack: $1000\n", "Score: 3/3\n", "\n", "🎯 GTO DECISION QUESTION:\n", "Scenario: Opponent bets $25 into $100 pot\n", "\n", "Your equity: 90.5%\n", "Pot odds: 20.0%\n", "\n", "What should you do?\n", "1. FOLD\n", "2. CALL\n", "3. RAISE\n", "Invalid input. Please enter a number between 1 and 3.\n", "\n", "📋 RIVER:\n", "\n", "============================================================\n", "POKER TRAINER - RIVER STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♠</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">10♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">A♦</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">3♥</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: Three of a Kind\n", "\n", "Pot Size: $100\n", "Current Bet: $25\n", "Your Stack: $1000\n", "Score: 3/3\n", "\n", "🎯 HAND RANKING QUESTION:\n", "What is your current best hand?\n", "1. Two Pair\n", "2. <PERSON> House\n", "3. Three of a Kind\n", "4. <PERSON>\n", "Invalid input. Please enter a number between 1 and 4.\n", "\n", "============================================================\n", "POKER TRAINER - RIVER STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♠</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">10♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">9♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">A♦</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">3♥</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: Three of a Kind\n", "\n", "Pot Size: $100\n", "Current Bet: $25\n", "Your Stack: $1000\n", "Score: 3/3\n", "\n", "🎯 GTO DECISION QUESTION:\n", "Scenario: Opponent bets $75 into $150 pot\n", "\n", "Your equity: 100.0%\n", "Pot odds: 33.3%\n", "\n", "What should you do?\n", "1. FOLD\n", "2. CALL\n", "3. RAISE\n", "❌ Incorrect. GTO recommendation: RAISE\n", "\n", "📊 Analysis:\n", "Your equity: 100.0%\n", "Pot odds required: 33.3%\n", "✅ You have sufficient equity to call\n", "\n", "📊 Hand 1 Complete!\n", "Current Score: 3/4 (75.0%)\n", "\n", "============================================================\n", "HAND 2 of 5\n", "============================================================\n", "\n", "📋 PREFLOP:\n", "\n", "============================================================\n", "POKER TRAINER - PREFLOP STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">8♠</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">2♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Pot Size: $100\n", "Current Bet: $0\n", "Your Stack: $1000\n", "Score: 3/4\n", "\n", "📋 FLOP:\n", "\n", "============================================================\n", "POKER TRAINER - FLOP STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">8♠</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">2♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">3♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">5♣</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: High Card\n", "\n", "Pot Size: $100\n", "Current Bet: $0\n", "Your Stack: $1000\n", "Score: 3/4\n", "\n", "🎯 HAND RANKING QUESTION:\n", "What is your current best hand?\n", "1. Two Pair\n", "2. <PERSON>\n", "3. High Card\n", "4. <PERSON> House\n", "✅ Correct! Well done!\n"]}, {"data": {"text/html": ["<h4>Your best 5-card hand:</h4><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">8♠</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">2♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">3♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">5♣</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "POKER TRAINER - FLOP STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">8♠</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">2♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">3♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">5♣</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: High Card\n", "\n", "Pot Size: $100\n", "Current Bet: $0\n", "Your Stack: $1000\n", "Score: 4/5\n", "\n", "🎯 GTO DECISION QUESTION:\n", "Scenario: Opponent bets $50 into $100 pot\n", "\n", "Your equity: 21.4%\n", "Pot odds: 33.3%\n", "\n", "What should you do?\n", "1. FOLD\n", "2. CALL\n", "3. RAISE\n", "✅ Correct! That's the GTO play!\n", "\n", "📊 Analysis:\n", "Your equity: 21.4%\n", "Pot odds required: 33.3%\n", "❌ Insufficient equity to call profitably\n", "\n", "📋 TURN:\n", "\n", "============================================================\n", "POKER TRAINER - TURN STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">8♠</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">2♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">3♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">5♣</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">K♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: High Card\n", "\n", "Pot Size: $100\n", "Current Bet: $50\n", "Your Stack: $1000\n", "Score: 5/6\n", "\n", "🎯 HAND RANKING QUESTION:\n", "What is your current best hand?\n", "1. Two Pair\n", "2. One Pair\n", "3. Four of a Kind\n", "4. High Card\n", "Invalid input. Please enter a number between 1 and 4.\n", "\n", "============================================================\n", "POKER TRAINER - TURN STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">8♠</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">2♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">3♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">5♣</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">K♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: High Card\n", "\n", "Pot Size: $100\n", "Current Bet: $50\n", "Your Stack: $1000\n", "Score: 5/6\n", "\n", "🎯 GTO DECISION QUESTION:\n", "Scenario: Opponent bets $25 into $100 pot\n", "\n", "Your equity: 9.7%\n", "Pot odds: 20.0%\n", "\n", "What should you do?\n", "1. FOLD\n", "2. CALL\n", "3. RAISE\n", "Invalid input. Please enter a number between 1 and 3.\n", "\n", "📋 RIVER:\n", "\n", "============================================================\n", "POKER TRAINER - RIVER STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">8♠</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">2♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">3♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">5♣</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">K♦</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">3♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: One Pair\n", "\n", "Pot Size: $100\n", "Current Bet: $25\n", "Your Stack: $1000\n", "Score: 5/6\n", "\n", "🎯 HAND RANKING QUESTION:\n", "What is your current best hand?\n", "1. <PERSON>\n", "2. <PERSON>\n", "3. One Pair\n", "4. <PERSON> House\n", "Invalid input. Please enter a number between 1 and 4.\n", "\n", "============================================================\n", "POKER TRAINER - RIVER STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">8♠</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">2♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">3♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">5♣</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♣</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">K♦</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">3♦</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: One Pair\n", "\n", "Pot Size: $100\n", "Current Bet: $25\n", "Your Stack: $1000\n", "Score: 5/6\n", "\n", "🎯 GTO DECISION QUESTION:\n", "Scenario: Opponent bets $50 into $100 pot\n", "\n", "Your equity: 100.0%\n", "Pot odds: 33.3%\n", "\n", "What should you do?\n", "1. FOLD\n", "2. CALL\n", "3. RAISE\n", "Invalid input. Please enter a number between 1 and 3.\n", "\n", "📊 Hand 2 Complete!\n", "Current Score: 5/6 (83.3%)\n", "\n", "============================================================\n", "HAND 3 of 5\n", "============================================================\n", "\n", "📋 PREFLOP:\n", "\n", "============================================================\n", "POKER TRAINER - PREFLOP STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♠</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">2♥</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Pot Size: $100\n", "Current Bet: $0\n", "Your Stack: $1000\n", "Score: 5/6\n", "\n", "📋 FLOP:\n", "\n", "============================================================\n", "POKER TRAINER - FLOP STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♠</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">2♥</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">A♦</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">K♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">A♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: One Pair\n", "\n", "Pot Size: $100\n", "Current Bet: $0\n", "Your Stack: $1000\n", "Score: 5/6\n", "\n", "🎯 HAND RANKING QUESTION:\n", "What is your current best hand?\n", "1. <PERSON>\n", "2. One Pair\n", "3. High Card\n", "4. Two Pair\n", "Invalid input. Please enter a number between 1 and 4.\n", "\n", "============================================================\n", "POKER TRAINER - FLOP STAGE\n", "============================================================\n"]}, {"data": {"text/html": ["<h3>Your Hole Cards:</h3><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">7♠</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">2♥</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>Community Cards:</h3><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">A♦</span><span style=\"color: red; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">K♥</span><span style=\"color: black; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">A♣</span>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Current Best Hand: One Pair\n", "\n", "Pot Size: $100\n", "Current Bet: $0\n", "Your Stack: $1000\n", "Score: 5/6\n", "\n", "🎯 GTO DECISION QUESTION:\n", "Sc<PERSON>rio: No bet to you\n", "\n", "Your equity: 24.5%\n", "\n", "What should you do?\n", "1. CHECK\n", "2. BET/RAISE\n"]}, {"ename": "KeyboardInterrupt", "evalue": "Interrupted by user", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[8], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Start the training session\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;66;03m# Run this cell to begin your poker training!\u001b[39;00m\n\u001b[0;32m----> 3\u001b[0m \u001b[43mrun_training_session\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "Cell \u001b[0;32mIn[7], line 32\u001b[0m, in \u001b[0;36mrun_training_session\u001b[0;34m()\u001b[0m\n\u001b[1;32m     29\u001b[0m \u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mPress Enter to continue...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     31\u001b[0m \u001b[38;5;66;03m# Ask GTO decision on flop\u001b[39;00m\n\u001b[0;32m---> 32\u001b[0m \u001b[43mask_gto_decision_question\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     33\u001b[0m \u001b[38;5;28minput\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m<PERSON>ress Enter to see the turn...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     35\u001b[0m \u001b[38;5;66;03m# Turn\u001b[39;00m\n", "Cell \u001b[0;32mIn[6], line 41\u001b[0m, in \u001b[0;36mask_gto_decision_question\u001b[0;34m()\u001b[0m\n\u001b[1;32m     38\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mi\u001b[38;5;241m+\u001b[39m\u001b[38;5;241m1\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m. \u001b[39m\u001b[38;5;132;01m{\u001b[39;00moption\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     40\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 41\u001b[0m     answer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mint\u001b[39m(\u001b[38;5;28;43minput\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;124;43mf\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;130;43;01m\\n\u001b[39;49;00m\u001b[38;5;124;43mEnter your choice (1-\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m): \u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m) \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m     42\u001b[0m     game\u001b[38;5;241m.\u001b[39mquestions_asked \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;241m1\u001b[39m\n\u001b[1;32m     44\u001b[0m     player_action \u001b[38;5;241m=\u001b[39m options[answer]\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/ipykernel/kernelbase.py:1270\u001b[0m, in \u001b[0;36mKernel.raw_input\u001b[0;34m(self, prompt)\u001b[0m\n\u001b[1;32m   1268\u001b[0m     msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mraw_input was called, but this frontend does not support input requests.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1269\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m StdinNotImplementedError(msg)\n\u001b[0;32m-> 1270\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_input_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1271\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mstr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mprompt\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1272\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_parent_ident\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mshell\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1273\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_parent\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mshell\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1274\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpassword\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   1275\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/mainenv/lib/python3.12/site-packages/ipykernel/kernelbase.py:1313\u001b[0m, in \u001b[0;36mKernel._input_request\u001b[0;34m(self, prompt, ident, parent, password)\u001b[0m\n\u001b[1;32m   1310\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m:\n\u001b[1;32m   1311\u001b[0m     \u001b[38;5;66;03m# re-raise KeyboardInterrupt, to truncate traceback\u001b[39;00m\n\u001b[1;32m   1312\u001b[0m     msg \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mInterrupted by user\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m-> 1313\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyboardInterrupt\u001b[39;00m(msg) \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m   1314\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[1;32m   1315\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mlog\u001b[38;5;241m.\u001b[39mwarning(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mInvalid Message:\u001b[39m\u001b[38;5;124m\"\u001b[39m, exc_info\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: Interrupted by user"]}], "source": ["# Start the training session\n", "# Run this cell to begin your poker training!\n", "run_training_session()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How to Use This Poker Trainer\n", "\n", "1. **Run all cells above** to initialize the poker trainer\n", "2. **Run the training session** by executing the cell above\n", "3. **Follow the prompts** to answer questions about:\n", "   - Hand rankings (what's your best hand?)\n", "   - GTO decisions (should you fold, call, or raise?)\n", "\n", "### What You'll Learn:\n", "- **Hand Recognition**: Quickly identify poker hands from high card to straight flush\n", "- **Equity Calculation**: Understand your winning chances in different situations\n", "- **GTO Strategy**: Learn optimal decision-making based on pot odds and equity\n", "- **Position Play**: How your position affects optimal strategy\n", "\n", "### Features:\n", "- 🎴 **Visual card display** with colored suits\n", "- 🎯 **Interactive multiple choice** questions\n", "- 📊 **Real-time equity calculations** using Monte Carlo simulation\n", "- 🧠 **GTO analysis** with explanations\n", "- 📈 **Progress tracking** with accuracy scoring\n", "\n", "The trainer uses simplified GTO principles and Monte Carlo simulation to calculate equity and provide optimal decision recommendations. Keep practicing to improve your poker skills!\n", "\n", "**Tip**: Pay attention to the equity percentages and pot odds explanations to understand the mathematical foundation of good poker decisions."]}], "metadata": {"kernelspec": {"display_name": "mainenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 4}