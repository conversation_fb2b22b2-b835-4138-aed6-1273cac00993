{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Poker Trainer - GTO Decision Making (Improved)\n", "\n", "This notebook simulates a poker table and tests your decision-making skills using Game Theory Optimal (GTO) principles.\n", "\n", "**New Features:**\n", "- Clear screen between steps for better focus\n", "- Preflop GTO questions\n", "- Game ends when player folds\n", "- Complete session summary at the end"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import itertools\n", "from collections import Counter\n", "from IPython.display import display, HTML, clear_output\n", "import ipywidgets as widgets\n", "from ipywidgets import interact, interactive, fixed, interact_manual\n", "\n", "# Card representation\n", "SUITS = ['♠', '♥', '♦', '♣']\n", "RANKS = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']\n", "RANK_VALUES = {rank: i for i, rank in enumerate(RANKS)}\n", "\n", "class Card:\n", "    def __init__(self, rank, suit):\n", "        self.rank = rank\n", "        self.suit = suit\n", "        self.value = RANK_VALUES[rank]\n", "    \n", "    def __str__(self):\n", "        return f\"{self.rank}{self.suit}\"\n", "    \n", "    def __repr__(self):\n", "        return self.__str__()\n", "    \n", "    def display_html(self):\n", "        color = 'red' if self.suit in ['♥', '♦'] else 'black'\n", "        return f'<span style=\"color: {color}; font-size: 24px; font-weight: bold; border: 2px solid #333; padding: 8px; margin: 2px; background: white; border-radius: 8px; display: inline-block; min-width: 50px; text-align: center;\">{self.rank}{self.suit}</span>'\n", "\n", "class Deck:\n", "    def __init__(self):\n", "        self.cards = [Card(rank, suit) for suit in SUITS for rank in RANKS]\n", "        self.shuffle()\n", "    \n", "    def shuffle(self):\n", "        random.shuffle(self.cards)\n", "    \n", "    def deal(self, num_cards=1):\n", "        return [self.cards.pop() for _ in range(num_cards)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Hand evaluation functions\n", "def evaluate_hand(cards):\n", "    \"\"\"Evaluate a 5-7 card poker hand and return the best 5-card hand\"\"\"\n", "    if len(cards) < 5:\n", "        return None, 0\n", "    \n", "    best_hand = None\n", "    best_rank = 0\n", "    \n", "    # Try all combinations of 5 cards\n", "    for combo in itertools.combinations(cards, 5):\n", "        hand_rank = get_hand_rank(list(combo))\n", "        if hand_rank > best_rank:\n", "            best_rank = hand_rank\n", "            best_hand = list(combo)\n", "    \n", "    return best_hand, best_rank\n", "\n", "def get_hand_rank(cards):\n", "    \"\"\"Get numerical rank of a 5-card hand (higher = better)\"\"\"\n", "    ranks = sorted([card.value for card in cards], reverse=True)\n", "    suits = [card.suit for card in cards]\n", "    \n", "    is_flush = len(set(suits)) == 1\n", "    is_straight = ranks == list(range(ranks[0], ranks[0] - 5, -1))\n", "    \n", "    # Special case for A-2-3-4-5 straight\n", "    if ranks == [12, 3, 2, 1, 0]:  # A, 5, 4, 3, 2\n", "        is_straight = True\n", "        ranks = [3, 2, 1, 0, 12]  # Treat ace as low\n", "    \n", "    rank_counts = Counter(ranks)\n", "    counts = sorted(rank_counts.values(), reverse=True)\n", "    \n", "    # Hand rankings (higher number = better hand)\n", "    if is_straight and is_flush:\n", "        return 8000000 + ranks[0]  # Straight flush\n", "    elif counts == [4, 1]:\n", "        return 7000000 + max(rank_counts, key=rank_counts.get) * 1000 + min(rank_counts, key=rank_counts.get)  # Four of a kind\n", "    elif counts == [3, 2]:\n", "        trips = max(rank_counts, key=rank_counts.get)\n", "        pair = min(rank_counts, key=rank_counts.get)\n", "        return 6000000 + trips * 1000 + pair  # Full house\n", "    elif is_flush:\n", "        return 5000000 + sum(rank * (13 ** i) for i, rank in enumerate(ranks))  # Flush\n", "    elif is_straight:\n", "        return 4000000 + ranks[0]  # Straight\n", "    elif counts == [3, 1, 1]:\n", "        trips = max(rank_counts, key=rank_counts.get)\n", "        kickers = sorted([r for r in ranks if r != trips], reverse=True)\n", "        return 3000000 + trips * 10000 + kickers[0] * 100 + kickers[1]  # Three of a kind\n", "    elif counts == [2, 2, 1]:\n", "        pairs = sorted([r for r, c in rank_counts.items() if c == 2], reverse=True)\n", "        kicker = [r for r, c in rank_counts.items() if c == 1][0]\n", "        return 2000000 + pairs[0] * 10000 + pairs[1] * 100 + kicker  # Two pair\n", "    elif counts == [2, 1, 1, 1]:\n", "        pair = [r for r, c in rank_counts.items() if c == 2][0]\n", "        kickers = sorted([r for r in ranks if r != pair], reverse=True)\n", "        return 1000000 + pair * 100000 + sum(k * (13 ** i) for i, k in enumerate(kickers))  # One pair\n", "    else:\n", "        return sum(rank * (13 ** i) for i, rank in enumerate(ranks))  # High card\n", "\n", "def get_hand_name(cards):\n", "    \"\"\"Get the name of a poker hand\"\"\"\n", "    if not cards:\n", "        return \"No hand\"\n", "    \n", "    ranks = sorted([card.value for card in cards], reverse=True)\n", "    suits = [card.suit for card in cards]\n", "    \n", "    is_flush = len(set(suits)) == 1\n", "    is_straight = ranks == list(range(ranks[0], ranks[0] - 5, -1))\n", "    \n", "    # Special case for A-2-3-4-5 straight\n", "    if ranks == [12, 3, 2, 1, 0]:\n", "        is_straight = True\n", "    \n", "    rank_counts = Counter(ranks)\n", "    counts = sorted(rank_counts.values(), reverse=True)\n", "    \n", "    if is_straight and is_flush:\n", "        return \"Straight Flush\"\n", "    elif counts == [4, 1]:\n", "        return \"Four of a Kind\"\n", "    elif counts == [3, 2]:\n", "        return \"Full House\"\n", "    elif is_flush:\n", "        return \"Flush\"\n", "    elif is_straight:\n", "        return \"Straight\"\n", "    elif counts == [3, 1, 1]:\n", "        return \"Three of a Kind\"\n", "    elif counts == [2, 2, 1]:\n", "        return \"Two Pair\"\n", "    elif counts == [2, 1, 1, 1]:\n", "        return \"One Pair\"\n", "    else:\n", "        return \"High Card\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Poker odds and equity calculations\n", "def calculate_equity(hole_cards, community_cards, num_simulations=10000):\n", "    \"\"\"Calculate equity using Monte Carlo simulation\"\"\"\n", "    if len(community_cards) == 5:\n", "        # All cards dealt, just evaluate\n", "        best_hand, hand_rank = evaluate_hand(hole_cards + community_cards)\n", "        return 1.0 if hand_rank > 0 else 0.0\n", "    \n", "    wins = 0\n", "    used_cards = set(str(card) for card in hole_cards + community_cards)\n", "    \n", "    for _ in range(num_simulations):\n", "        # Create a deck without used cards\n", "        available_cards = [Card(rank, suit) for suit in SUITS for rank in RANKS \n", "                          if f\"{rank}{suit}\" not in used_cards]\n", "        random.shuffle(available_cards)\n", "        \n", "        # Complete the community cards\n", "        cards_needed = 5 - len(community_cards)\n", "        simulated_community = community_cards + available_cards[:cards_needed]\n", "        \n", "        # Simulate opponent with random hole cards\n", "        opponent_cards = available_cards[cards_needed:cards_needed + 2]\n", "        \n", "        # Evaluate both hands\n", "        player_hand, player_rank = evaluate_hand(hole_cards + simulated_community)\n", "        opponent_hand, opponent_rank = evaluate_hand(opponent_cards + simulated_community)\n", "        \n", "        if player_rank > opponent_rank:\n", "            wins += 1\n", "        elif player_rank == opponent_rank:\n", "            wins += 0.5  # Tie\n", "    \n", "    return wins / num_simulations\n", "\n", "def pot_odds_decision(pot_size, bet_size, equity):\n", "    \"\"\"Calculate if a call is profitable based on pot odds\"\"\"\n", "    pot_odds = bet_size / (pot_size + bet_size)\n", "    return equity > pot_odds, pot_odds\n", "\n", "def gto_action_recommendation(equity, position, pot_size, bet_size, stack_size):\n", "    \"\"\"Simplified GTO recommendation based on equity and position\"\"\"\n", "    pot_odds = bet_size / (pot_size + bet_size) if bet_size > 0 else 0\n", "    \n", "    # Simplified GTO thresholds\n", "    if equity > 0.65:\n", "        return \"RAISE\" if stack_size > bet_size * 3 else \"CALL\"\n", "    elif equity > pot_odds + 0.05:  # Small edge for position\n", "        return \"CALL\"\n", "    elif equity > 0.3 and bet_size == 0:  # Check with decent equity\n", "        return \"CHECK\"\n", "    elif bet_size == 0 and equity < 0.3:\n", "        return \"CHECK\"  # Don't bet weak hands\n", "    else:\n", "        return \"FOLD\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Improved Poker game simulation\n", "class PokerGame:\n", "    def __init__(self):\n", "        self.deck = Deck()\n", "        self.hole_cards = []\n", "        self.community_cards = []\n", "        self.pot_size = 100\n", "        self.current_bet = 0\n", "        self.player_stack = 1000\n", "        self.stage = \"preflop\"  # preflop, flop, turn, river\n", "        self.score = 0\n", "        self.questions_asked = 0\n", "        self.hand_results = []  # Store results for final summary\n", "        self.current_hand_log = []  # Log for current hand\n", "        self.session_log = []  # Complete session log\n", "    \n", "    def start_new_hand(self):\n", "        self.deck = Deck()\n", "        self.hole_cards = self.deck.deal(2)\n", "        self.community_cards = []\n", "        self.pot_size = 100\n", "        self.current_bet = 0\n", "        self.stage = \"preflop\"\n", "        self.current_hand_log = []  # Reset log for new hand\n", "    \n", "    def deal_flop(self):\n", "        if self.stage == \"preflop\":\n", "            self.community_cards.extend(self.deck.deal(3))\n", "            self.stage = \"flop\"\n", "    \n", "    def deal_turn(self):\n", "        if self.stage == \"flop\":\n", "            self.community_cards.extend(self.deck.deal(1))\n", "            self.stage = \"turn\"\n", "    \n", "    def deal_river(self):\n", "        if self.stage == \"turn\":\n", "            self.community_cards.extend(self.deck.deal(1))\n", "            self.stage = \"river\"\n", "    \n", "    def display_table(self):\n", "        \"\"\"Display the current poker table state\"\"\"\n", "        print(\"\\n\" + \"=\"*60)\n", "        print(f\"POKER TRAINER - {self.stage.upper()} STAGE\")\n", "        print(\"=\"*60)\n", "        \n", "        # Display hole cards\n", "        hole_html = \"<h3>Your Hole Cards:</h3>\" + \"\".join([card.display_html() for card in self.hole_cards])\n", "        display(HTML(hole_html))\n", "        \n", "        # Display community cards\n", "        if self.community_cards:\n", "            community_html = \"<h3>Community Cards:</h3>\" + \"\".join([card.display_html() for card in self.community_cards])\n", "            display(HTML(community_html))\n", "        \n", "        # Display current hand strength\n", "        if len(self.community_cards) >= 3:\n", "            best_hand, _ = evaluate_hand(self.hole_cards + self.community_cards)\n", "            hand_name = get_hand_name(best_hand)\n", "            print(f\"\\nCurrent Best Hand: {hand_name}\")\n", "        \n", "        print(f\"\\nPot Size: ${self.pot_size}\")\n", "        print(f\"Current Bet: ${self.current_bet}\")\n", "        print(f\"Your Stack: ${self.player_stack}\")\n", "        print(f\"Score: {self.score}/{self.questions_asked}\")\n", "    \n", "    def get_equity(self):\n", "        return calculate_equity(self.hole_cards, self.community_cards, 5000)\n", "    \n", "    def get_gto_action(self):\n", "        equity = self.get_equity()\n", "        return gto_action_recommendation(equity, \"middle\", self.pot_size, self.current_bet, self.player_stack)\n", "    \n", "    def get_preflop_gto_action(self):\n", "        \"\"\"Get GTO action for preflop based on hole cards\"\"\"\n", "        # Simplified preflop ranges\n", "        card1, card2 = self.hole_cards\n", "        \n", "        # Check for pairs\n", "        if card1.value == card2.value:\n", "            if card1.value >= 10:  # JJ, QQ, KK, AA\n", "                return \"RAISE\"\n", "            elif card1.value >= 7:  # 88, 99, TT\n", "                return \"CALL\"\n", "            else:  # 22-77\n", "                return \"CALL\"\n", "        \n", "        # Check for suited cards\n", "        is_suited = card1.suit == card2.suit\n", "        high_card = max(card1.value, card2.value)\n", "        low_card = min(card1.value, card2.value)\n", "        \n", "        # Premium hands\n", "        if high_card >= 12 and low_card >= 10:  # AK, AQ, KQ\n", "            return \"RAISE\"\n", "        elif high_card == 12 and low_card >= 8:  # AJ, AT, A9\n", "            return \"RAISE\" if is_suited else \"CALL\"\n", "        elif high_card >= 11 and low_card >= 9:  # KJ, KT, QJ, QT\n", "            return \"CALL\"\n", "        elif is_suited and high_card >= 10 and low_card >= 6:  # Suited connectors/one-gappers\n", "            return \"CALL\"\n", "        else:\n", "            return \"FOLD\"\n", "    \n", "    def add_hand_ranking_result(self, stage, correct_answer, player_answer, is_correct):\n", "        \"\"\"Add hand ranking question result to log\"\"\"\n", "        self.current_hand_log.append({\n", "            'type': 'hand_ranking',\n", "            'stage': stage,\n", "            'correct_answer': correct_answer,\n", "            'player_answer': player_answer,\n", "            'is_correct': is_correct\n", "        })\n", "    \n", "    def add_decision_result(self, stage, player_action, gto_action, is_correct, equity):\n", "        \"\"\"Add GTO decision result to log\"\"\"\n", "        self.current_hand_log.append({\n", "            'type': 'gto_decision',\n", "            'stage': stage,\n", "            'player_action': player_action,\n", "            'gto_action': gto_action,\n", "            'is_correct': is_correct,\n", "            'equity': equity\n", "        })\n", "    \n", "    def finish_hand(self):\n", "        \"\"\"Finish current hand and store results\"\"\"\n", "        hand_summary = {\n", "            'hole_cards': [str(card) for card in self.hole_cards],\n", "            'community_cards': [str(card) for card in self.community_cards],\n", "            'final_stage': self.stage,\n", "            'log': self.current_hand_log.copy()\n", "        }\n", "        self.hand_results.append(hand_summary)\n", "        self.session_log.append(hand_summary)\n", "\n", "# Initialize game\n", "game = PokerGame()\n", "print(\"Improved Poker Trainer initialized!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive quiz functions with screen clearing\n", "def ask_preflop_gto_question():\n", "    \"\"\"Ask player about optimal preflop GTO decision\"\"\"\n", "    clear_output(wait=True)\n", "    game.display_table()\n", "    \n", "    print(f\"\\n🎯 PREFLOP GTO DECISION QUESTION:\")\n", "    print(f\"You are in middle position. What should you do with these hole cards?\")\n", "    \n", "    # Get GTO recommendation\n", "    gto_action = game.get_preflop_gto_action()\n", "    \n", "    # Action options for preflop\n", "    options = [\"FOLD\", \"CALL\", \"RAISE\"]\n", "    \n", "    print(\"\\nWhat should you do?\")\n", "    for i, option in enumerate(options):\n", "        print(f\"{i+1}. {option}\")\n", "    \n", "    try:\n", "        answer = int(input(f\"\\nEnter your choice (1-{len(options)}): \")) - 1\n", "        game.questions_asked += 1\n", "        \n", "        player_action = options[answer]\n", "        \n", "        # Check if answer matches GTO recommendation\n", "        correct = player_action == gto_action\n", "        \n", "        if correct:\n", "            print(\"✅ Correct! That's the GTO play!\")\n", "            game.score += 1\n", "        else:\n", "            print(f\"❌ Incorrect. GTO recommendation: {gto_action}\")\n", "        \n", "        # Explain the reasoning\n", "        print(f\"\\n📊 Preflop Analysis:\")\n", "        card1, card2 = game.hole_cards\n", "        if card1.value == card2.value:\n", "            print(f\"You have a pocket pair: {card1.rank}{card1.rank}\")\n", "        elif card1.suit == card2.suit:\n", "            print(f\"You have suited cards: {card1.rank}{card2.rank}s\")\n", "        else:\n", "            print(f\"You have offsuit cards: {card1.rank}{card2.rank}o\")\n", "        \n", "        # Store the result for final summary\n", "        game.add_decision_result(game.stage, player_action, gto_action, correct, 0.0)\n", "        \n", "        # Return True if player folded (to end the hand)\n", "        return player_action == \"FOLD\"\n", "        \n", "    except (ValueError, IndexError):\n", "        print(f\"Invalid input. Please enter a number between 1 and {len(options)}.\")\n", "        return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def ask_hand_ranking_question():\n", "    \"\"\"Ask player to identify the most common hands\"\"\"\n", "    clear_output(wait=True)\n", "    game.display_table()\n", "    \n", "    if len(game.community_cards) < 3:\n", "        print(\"\\nNot enough community cards for hand ranking question.\")\n", "        return\n", "    \n", "    # Calculate possible hands\n", "    all_cards = game.hole_cards + game.community_cards\n", "    current_hand, _ = evaluate_hand(all_cards)\n", "    current_hand_name = get_hand_name(current_hand)\n", "    \n", "    # Generate options\n", "    hand_types = [\"High Card\", \"One Pair\", \"Two Pair\", \"Three of a Kind\", \n", "                  \"Straight\", \"Flush\", \"Full House\", \"Four of a Kind\", \"Straight Flush\"]\n", "    \n", "    # Create multiple choice\n", "    options = [current_hand_name]\n", "    while len(options) < 4:\n", "        option = random.choice(hand_types)\n", "        if option not in options:\n", "            options.append(option)\n", "    \n", "    random.shuffle(options)\n", "    correct_index = options.index(current_hand_name)\n", "    \n", "    print(\"\\n🎯 HAND RANKING QUESTION:\")\n", "    print(\"What is your current best hand?\")\n", "    for i, option in enumerate(options):\n", "        print(f\"{i+1}. {option}\")\n", "    \n", "    try:\n", "        answer = int(input(\"\\nEnter your choice (1-4): \")) - 1\n", "        game.questions_asked += 1\n", "        \n", "        correct = answer == correct_index\n", "        if correct:\n", "            print(\"✅ Correct! Well done!\")\n", "            game.score += 1\n", "        else:\n", "            print(f\"❌ Incorrect. The correct answer was: {current_hand_name}\")\n", "            \n", "        # Show the actual best 5-card hand\n", "        if current_hand:\n", "            hand_html = \"<h4>Your best 5-card hand:</h4>\" + \"\".join([card.display_html() for card in current_hand])\n", "            display(HTML(hand_html))\n", "        \n", "        # Store the result for final summary\n", "        game.add_hand_ranking_result(game.stage, current_hand_name, options[answer] if answer >= 0 else \"Invalid\", correct)\n", "        \n", "    except (ValueError, IndexError):\n", "        print(\"Invalid input. Please enter a number between 1 and 4.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def ask_gto_decision_question():\n", "    \"\"\"Ask player about optimal GTO decision\"\"\"\n", "    clear_output(wait=True)\n", "    game.display_table()\n", "    \n", "    # Set up a betting scenario\n", "    scenarios = [\n", "        {\"bet\": 0, \"pot\": 100, \"description\": \"No bet to you\"},\n", "        {\"bet\": 25, \"pot\": 100, \"description\": \"Opponent bets $25 into $100 pot\"},\n", "        {\"bet\": 50, \"pot\": 100, \"description\": \"Opponent bets $50 into $100 pot\"},\n", "        {\"bet\": 75, \"pot\": 150, \"description\": \"Opponent bets $75 into $150 pot\"}\n", "    ]\n", "    \n", "    scenario = random.choice(scenarios)\n", "    game.current_bet = scenario[\"bet\"]\n", "    game.pot_size = scenario[\"pot\"]\n", "    \n", "    print(f\"\\n🎯 GTO DECISION QUESTION:\")\n", "    print(f\"Scenario: {scenario['description']}\")\n", "    \n", "    # Calculate equity and GTO recommendation\n", "    equity = game.get_equity()\n", "    gto_action = game.get_gto_action()\n", "    \n", "    print(f\"\\nYour equity: {equity:.1%}\")\n", "    \n", "    if game.current_bet > 0:\n", "        pot_odds = game.current_bet / (game.pot_size + game.current_bet)\n", "        print(f\"Pot odds: {pot_odds:.1%}\")\n", "    \n", "    # Action options\n", "    if game.current_bet == 0:\n", "        options = [\"CHECK\", \"BET/RAISE\"]\n", "    else:\n", "        options = [\"FOLD\", \"CALL\", \"RAISE\"]\n", "    \n", "    print(\"\\nWhat should you do?\")\n", "    for i, option in enumerate(options):\n", "        print(f\"{i+1}. {option}\")\n", "    \n", "    try:\n", "        answer = int(input(f\"\\nEnter your choice (1-{len(options)}): \")) - 1\n", "        game.questions_asked += 1\n", "        \n", "        player_action = options[answer]\n", "        \n", "        # Check if answer matches GTO recommendation\n", "        correct = False\n", "        if gto_action == \"CHECK\" and player_action == \"CHECK\":\n", "            correct = True\n", "        elif gto_action == \"RAISE\" and player_action in [\"BET/RAISE\", \"RAISE\"]:\n", "            correct = True\n", "        elif gto_action == \"CALL\" and player_action == \"CALL\":\n", "            correct = True\n", "        elif gto_action == \"FOLD\" and player_action == \"FOLD\":\n", "            correct = True\n", "        \n", "        if correct:\n", "            print(\"✅ Correct! That's the GTO play!\")\n", "            game.score += 1\n", "        else:\n", "            print(f\"❌ Incorrect. GTO recommendation: {gto_action}\")\n", "        \n", "        # Explain the reasoning\n", "        print(f\"\\n📊 Analysis:\")\n", "        print(f\"Your equity: {equity:.1%}\")\n", "        if game.current_bet > 0:\n", "            pot_odds = game.current_bet / (game.pot_size + game.current_bet)\n", "            print(f\"Pot odds required: {pot_odds:.1%}\")\n", "            if equity > pot_odds:\n", "                print(\"✅ You have sufficient equity to call\")\n", "            else:\n", "                print(\"❌ Insufficient equity to call profitably\")\n", "        \n", "        # Store the result for final summary\n", "        game.add_decision_result(game.stage, player_action, gto_action, correct, equity)\n", "        \n", "        # Return True if player folded (to end the hand)\n", "        return player_action == \"FOLD\"\n", "        \n", "    except (ValueError, IndexError):\n", "        print(f\"Invalid input. Please enter a number between 1 and {len(options)}.\")\n", "        return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def print_session_summary():\n", "    \"\"\"Print complete session summary at the end\"\"\"\n", "    clear_output(wait=True)\n", "    \n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"🏆 POKER TRAINING SESSION COMPLETE - FULL SUMMARY\")\n", "    print(\"=\"*80)\n", "    \n", "    print(f\"\\n📊 OVERALL PERFORMANCE:\")\n", "    print(f\"Final Score: {game.score}/{game.questions_asked}\")\n", "    accuracy = game.score/max(1,game.questions_asked)*100\n", "    print(f\"Accuracy: {accuracy:.1f}%\")\n", "    \n", "    if accuracy >= 80:\n", "        print(\"🌟 Excellent work! You have strong poker fundamentals!\")\n", "    elif accuracy >= 60:\n", "        print(\"👍 Good job! Keep practicing to improve your game.\")\n", "    else:\n", "        print(\"📚 Keep studying! Practice makes perfect in poker.\")\n", "    \n", "    print(f\"\\n📋 DETAILED HAND-BY-HAND BREAKDOWN:\")\n", "    print(\"-\" * 80)\n", "    \n", "    for i, hand_result in enumerate(game.session_log, 1):\n", "        print(f\"\\n🃏 HAND {i}:\")\n", "        print(f\"Hole Cards: {', '.join(hand_result['hole_cards'])}\")\n", "        if hand_result['community_cards']:\n", "            print(f\"Community Cards: {', '.join(hand_result['community_cards'])}\")\n", "        print(f\"Final Stage: {hand_result['final_stage'].upper()}\")\n", "        \n", "        # Show all questions and answers for this hand\n", "        for j, log_entry in enumerate(hand_result['log'], 1):\n", "            if log_entry['type'] == 'hand_ranking':\n", "                status = \"✅\" if log_entry['is_correct'] else \"❌\"\n", "                print(f\"  Q{j}: Hand Ranking ({log_entry['stage']}) {status}\")\n", "                print(f\"      Correct: {log_entry['correct_answer']}\")\n", "                print(f\"      Your Answer: {log_entry['player_answer']}\")\n", "            elif log_entry['type'] == 'gto_decision':\n", "                status = \"✅\" if log_entry['is_correct'] else \"❌\"\n", "                equity_str = f\" (Equity: {log_entry['equity']:.1%})\" if log_entry['equity'] > 0 else \"\"\n", "                print(f\"  Q{j}: GTO Decision ({log_entry['stage']}) {status}{equity_str}\")\n", "                print(f\"      GTO Recommendation: {log_entry['gto_action']}\")\n", "                print(f\"      Your Action: {log_entry['player_action']}\")\n", "        \n", "        if hand_result['final_stage'] == 'preflop':\n", "            print(f\"  ⚠️  Hand ended early (folded preflop)\")\n", "    \n", "    print(f\"\\n\" + \"=\"*80)\n", "    print(\"📚 KEY LEARNING POINTS:\")\n", "    print(\"• Hand recognition improves with practice\")\n", "    print(\"• GTO decisions are based on equity vs pot odds\")\n", "    print(\"• Preflop ranges determine which hands to play\")\n", "    print(\"• Position and stack sizes affect optimal strategy\")\n", "    print(\"\\n🎯 Keep practicing to master poker fundamentals!\")\n", "    print(\"=\"*80)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_improved_training_session():\n", "    \"\"\"Run a complete poker training session with improvements\"\"\"\n", "    clear_output(wait=True)\n", "    print(\"🃏 Welcome to the Improved Poker Trainer!\")\n", "    print(\"This program will test your poker knowledge and GTO decision-making.\")\n", "    print(\"\\n🆕 NEW FEATURES:\")\n", "    print(\"• Clear screen between steps for better focus\")\n", "    print(\"• Preflop GTO questions based on hole cards\")\n", "    print(\"• Game ends when you fold (realistic gameplay)\")\n", "    print(\"• Complete session summary at the end\")\n", "    print(\"\\n📋 Instructions:\")\n", "    print(\"- Answer questions about hand rankings and optimal decisions\")\n", "    print(\"- Learn from GTO analysis and improve your game!\")\n", "    print(\"- If you fold, the hand ends (just like real poker)\")\n", "    \n", "    input(\"\\nPress Enter to start your training session...\")\n", "    \n", "    num_hands = 5  # Number of hands to practice\n", "    \n", "    for hand_num in range(1, num_hands + 1):\n", "        clear_output(wait=True)\n", "        print(f\"\\n{'='*60}\")\n", "        print(f\"HAND {hand_num} of {num_hands}\")\n", "        print(f\"{'='*60}\")\n", "        \n", "        # Start new hand\n", "        game.start_new_hand()\n", "        \n", "        # Preflop - show cards and ask GTO question\n", "        folded = ask_preflop_gto_question()\n", "        \n", "        if folded:\n", "            print(\"\\n🔄 Hand ended - you folded preflop.\")\n", "            game.finish_hand()\n", "            input(\"\\nPress Enter to continue to next hand...\")\n", "            continue\n", "        \n", "        input(\"\\nPress Enter to see the flop...\")\n", "        \n", "        # Flop\n", "        game.deal_flop()\n", "        ask_hand_ranking_question()\n", "        input(\"\\nPress Enter to continue...\")\n", "        \n", "        # Ask GTO decision on flop\n", "        folded = ask_gto_decision_question()\n", "        if folded:\n", "            print(\"\\n🔄 Hand ended - you folded on the flop.\")\n", "            game.finish_hand()\n", "            input(\"\\nPress Enter to continue to next hand...\")\n", "            continue\n", "        \n", "        input(\"\\nPress Enter to see the turn...\")\n", "        \n", "        # Turn\n", "        game.deal_turn()\n", "        ask_hand_ranking_question()\n", "        input(\"\\nPress Enter to continue...\")\n", "        \n", "        # Ask GTO decision on turn\n", "        folded = ask_gto_decision_question()\n", "        if folded:\n", "            print(\"\\n🔄 Hand ended - you folded on the turn.\")\n", "            game.finish_hand()\n", "            input(\"\\nPress Enter to continue to next hand...\")\n", "            continue\n", "        \n", "        input(\"\\nPress Enter to see the river...\")\n", "        \n", "        # River\n", "        game.deal_river()\n", "        ask_hand_ranking_question()\n", "        input(\"\\nPress Enter to continue...\")\n", "        \n", "        # Final GTO decision\n", "        folded = ask_gto_decision_question()\n", "        if folded:\n", "            print(\"\\n🔄 Hand ended - you folded on the river.\")\n", "        \n", "        # Finish hand\n", "        game.finish_hand()\n", "        \n", "        # Show progress\n", "        print(f\"\\n📊 Hand {hand_num} Complete!\")\n", "        print(f\"Current Score: {game.score}/{game.questions_asked} ({game.score/max(1,game.questions_asked)*100:.1f}%)\")\n", "        \n", "        if hand_num < num_hands:\n", "            input(\"\\nPress Enter for next hand...\")\n", "    \n", "    # Show complete session summary\n", "    print_session_summary()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Start the improved training session\n", "# Run this cell to begin your poker training!\n", "run_improved_training_session()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Improved Poker Trainer Features\n", "\n", "### 🆕 What's New:\n", "\n", "1. **Clear Screen Between Steps**: Each question gets a clean screen for better focus\n", "2. **Preflop GTO Questions**: Learn optimal preflop decisions based on your hole cards\n", "3. **Realistic Folding**: Game ends when you fold (just like real poker)\n", "4. **Complete Session Summary**: Detailed breakdown of all hands and decisions at the end\n", "\n", "### 🎯 Training Flow:\n", "\n", "1. **Preflop**: GTO decision based on hole cards (Fold/Call/Raise)\n", "2. **Flop**: Hand ranking question + GTO decision\n", "3. **Turn**: Hand ranking question + GTO decision  \n", "4. **River**: Hand ranking question + GTO decision\n", "5. **Summary**: Complete breakdown of all decisions\n", "\n", "### 📊 What You'll Learn:\n", "\n", "- **Preflop Ranges**: Which hands to play from different positions\n", "- **Hand Recognition**: Quick identification of poker hands\n", "- **Equity vs Pot Odds**: Mathematical foundation of poker decisions\n", "- **GTO Strategy**: Optimal decision-making in various scenarios\n", "\n", "### 🎮 Realistic Gameplay:\n", "\n", "- If you fold at any stage, the hand ends (no more questions for that hand)\n", "- Each decision affects whether you continue in the hand\n", "- Learn the consequences of folding vs continuing\n", "\n", "**Ready to improve your poker skills? Run the cell above to start training!**"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}