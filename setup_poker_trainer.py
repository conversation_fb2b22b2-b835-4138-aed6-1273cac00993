#!/usr/bin/env python3
"""
Setup script for Poker Trainer
This script helps set up the environment and launch the Jupyter notebook.
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
    except subprocess.CalledProcessError:
        print("❌ Failed to install requirements. Please install manually:")
        print("pip install jupyter ipywidgets numpy")
        return False
    return True

def enable_jupyter_widgets():
    """Enable Jupyter widgets extension"""
    print("Enabling Jupyter widgets...")
    try:
        subprocess.check_call([sys.executable, "-m", "jupyter", "nbextension", "enable", "--py", "widgetsnbextension"])
        print("✅ Jupyter widgets enabled!")
    except subprocess.CalledProcessError:
        print("⚠️  Could not enable widgets extension. The trainer will still work.")

def launch_notebook():
    """Launch Jupyter notebook"""
    print("Launching Jupyter notebook...")
    try:
        subprocess.check_call([sys.executable, "-m", "jupyter", "notebook", "poker_trainer.ipynb"])
    except subprocess.CalledProcessError:
        print("❌ Failed to launch notebook. Please run manually:")
        print("jupyter notebook poker_trainer.ipynb")

def main():
    print("🃏 Poker Trainer Setup")
    print("=" * 30)
    
    if not os.path.exists("poker_trainer.ipynb"):
        print("❌ poker_trainer.ipynb not found in current directory!")
        return
    
    if install_requirements():
        enable_jupyter_widgets()
        
        print("\n🚀 Setup complete!")
        print("The Jupyter notebook will now open.")
        print("Follow the instructions in the notebook to start training!")
        
        launch_notebook()
    else:
        print("\n❌ Setup failed. Please install requirements manually and run:")
        print("jupyter notebook poker_trainer.ipynb")

if __name__ == "__main__":
    main()
