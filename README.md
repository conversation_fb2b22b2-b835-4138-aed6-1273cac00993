# 🃏 Poker Trainer - GTO Decision Making

An interactive Python notebook that simulates a poker table and tests your poker knowledge using Game Theory Optimal (GTO) principles.

## Features

- 🎴 **Visual Card Display**: Beautiful card representations with colored suits
- 🎯 **Interactive Quizzes**: Multiple choice questions about hand rankings and decisions
- 📊 **Real-time Equity Calculations**: Monte Carlo simulation for accurate win probabilities
- 🧠 **GTO Analysis**: Learn optimal decision-making with detailed explanations
- 📈 **Progress Tracking**: Track your accuracy and improvement over time

## What You'll Learn

### Hand Recognition
- Quickly identify poker hands from high card to straight flush
- Understand hand rankings and relative strength
- Practice with real poker scenarios

### GTO Strategy
- Learn when to fold, call, check, or raise
- Understand pot odds and equity calculations
- Make mathematically optimal decisions

### Poker Fundamentals
- Equity vs pot odds analysis
- Position-based decision making
- Risk vs reward evaluation

## Quick Start

### Option 1: Automatic Setup (Recommended)
```bash
python setup_poker_trainer.py
```

### Option 2: Manual Setup
1. Install requirements:
   ```bash
   pip install jupyter ipywidgets numpy
   ```

2. Enable Jupyter widgets:
   ```bash
   jupyter nbextension enable --py widgetsnbextension
   ```

3. Launch the notebook:
   ```bash
   jupyter notebook poker_trainer.ipynb
   ```

## How to Use

1. **Open the notebook** and run all cells to initialize the poker trainer
2. **Start training** by running the final cell with `run_training_session()`
3. **Follow prompts** to answer questions about:
   - Hand rankings (What's your best hand?)
   - GTO decisions (Should you fold, call, or raise?)
4. **Learn from feedback** with detailed equity analysis and explanations

## Training Session Structure

Each training session includes:
- **5 complete poker hands** from preflop to river
- **Hand ranking questions** at flop, turn, and river
- **GTO decision questions** with betting scenarios
- **Real-time feedback** with explanations
- **Final scoring** and performance analysis

## Example Questions

### Hand Ranking
*"Looking at your hole cards and the community cards, what is your current best hand?"*
- Multiple choice between different hand types
- Visual display of your actual best 5-card hand

### GTO Decisions
*"Opponent bets $50 into a $100 pot. What should you do?"*
- Options: Fold, Call, Raise
- Analysis includes your equity vs required pot odds
- Explanation of optimal GTO strategy

## Technical Details

- **Hand Evaluation**: Comprehensive 5-7 card hand evaluation
- **Equity Calculation**: Monte Carlo simulation (5,000+ iterations)
- **GTO Logic**: Simplified but mathematically sound decision trees
- **Card Display**: HTML/CSS rendering with suit colors

## Requirements

- Python 3.6+
- Jupyter Notebook
- ipywidgets (for interactive elements)
- numpy (for calculations)

## Educational Value

This trainer helps you develop:
- **Pattern Recognition**: Quickly spot hand types and draws
- **Mathematical Thinking**: Understand probability and expected value
- **Decision Making**: Learn to make optimal choices under uncertainty
- **Poker Intuition**: Build instincts based on solid fundamentals

## Tips for Best Results

1. **Pay attention to equity percentages** - they're the foundation of good decisions
2. **Understand pot odds** - compare them to your equity for call/fold decisions
3. **Practice regularly** - poker skills improve with repetition
4. **Study the explanations** - learn why certain decisions are optimal
5. **Track your progress** - aim for 80%+ accuracy

## Customization

The notebook is designed to be educational and can be modified:
- Adjust number of hands per session
- Modify GTO thresholds for different strategies
- Add new question types or scenarios
- Change simulation parameters for speed vs accuracy

## Disclaimer

This trainer uses simplified GTO principles for educational purposes. Real poker involves many additional factors like opponent tendencies, table dynamics, and advanced game theory concepts. Use this as a foundation for learning poker fundamentals.

---

**Happy Training! 🎯**

*Remember: Good poker decisions are based on mathematics, not luck. This trainer will help you understand the math behind optimal play.*
